import { CellContext } from '@tanstack/react-table';
import { ActionButtons } from './TenantPage';


export const DEFAULT_LIMIT = 5;
export const DEFAULT_OFFSET = 0;

export const tenantTableColumns = [
    { header: 'Tenant name', accessorKey: 'name', id: 'tenantName' },
    { header: 'Status', accessorKey: 'status', id: 'status' },
    {
        header: 'Created date',
        accessorKey: 'createdOn',
        id: 'createdDate',
        cell: ({ row }: CellContext<any, any>) => {
            const date = new Date(row.original.createdOn);
            const day = date.toLocaleDateString('en-GB', { day: '2-digit' });
            const month = date.toLocaleDateString('en-GB', { month: 'long' });
            const year = date.toLocaleDateString('en-GB', { year: 'numeric' });
            return `${day} ${month}, ${year}`;
        },
    },
    { header: 'Plan name', accessorKey: 'planName', id: 'planName' },
    {
        header: 'Actions',
        cell: ({ row }: CellContext<any, any>) => <ActionButtons row={row.original} />,
    },

]
