import { Box, IconButton, InputAdornment, Stack, TextField, Typography } from '@mui/material';
import EyeIcon from 'Assets/EyeIcon';
import { Table } from 'Components/Table';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Add, Search } from '@mui/icons-material';
import { CellContext } from '@tanstack/react-table';
import DotIcon from 'Assets/DotIcon';
import EditIcon from 'Assets/EditIcon';
import FilterIcon from 'Assets/FilterIcon';
import Button from 'Components/Button';
import { useGetTenantsQuery } from 'redux/app/tenantManagementApiSlice';
import { actionStyles, bodyCellProps, bodyRowProps, buttonStyle, coloumnCellProps, headerBoxStyle, leftHeaderStyle, tableHeadProps } from 'styles/pages/TenantPage.styles';
import { DEFAULT_LIMIT, DEFAULT_OFFSET, tenantTableColumns } from './tenants.utils';



interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
}


export const ActionButtons: React.FC<IActionButtonsProps> = ({ row }) => {
  const handleEditBtn = (): void => {
    console.log('Edit', row);
  };

  return (
    <Stack display="flex" flexDirection={'row'}>
      <EyeIcon sx={actionStyles} />
      <EditIcon sx={actionStyles} />
      <DotIcon sx={actionStyles} />
    </Stack>
  );
};

const Tenant: React.FC = () => {
  const [limit, setLimit] = useState(DEFAULT_LIMIT);
  const [searchTerm, setSearchTerm] = useState('');
  const [offset, setOffset] = useState(DEFAULT_OFFSET);
  const [sortBy, setSortBy] = useState<string | null>(null);
  const { data: tenants, error, isLoading } = useGetTenantsQuery({
    filter: JSON.stringify({
      limit: 12
    })
  });
  const navigate = useNavigate();

  const handleRedirect = () => {
    navigate('/add-tenant');
  };



  return (
    // <Box data-testid="TenantPage">
    //   {/* First Row */}
    //   <Box display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap" mb={2}>
    //     <Typography variant="h6" component="div">
    //       Tenants
    //     </Typography>
    //     <Box display="flex" alignItems="center" gap={2}>
    //       <Formik
    //         initialValues={{ search: '' }}
    //         onSubmit={(values: { search: string }, helpers: FormikHelpers<{ search: string }>) => {
    //           console.log('Search submitted:', values);
    //         }}
    //       >
    //         {({ handleSubmit }: { handleSubmit: () => void }) => (
    //           <form onSubmit={handleSubmit} style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
    //             <FormInput
    //               id="search"
    //               name="search"
    //               placeholder="Search tenant name"
    //               required={false}
    //               readOnly={false}
    //             />
    //           </form>
    //         )}
    //       </Formik>
    //       <FilterIcon style={{ color: colors.white }} sx={{ border: '1px solid black', padding: '5px' }} />
    //       <Button onClick={handleRedirect} className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
    //         + Add Tenant
    //       </Button>
    //     </Box>
    //   </Box>
    <Box>
      {/* Header */}
      <Box sx={headerBoxStyle}>
        <Typography variant='h6' sx={leftHeaderStyle}>
          Tenants
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="Search tenant name"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search sx={{ color: '#6c757d', fontSize: 20 }} />
                </InputAdornment>
              ),
            }}
            sx={{
              width: 250,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'white',
                fontSize: '14px',
              }
            }}
          />
          <IconButton
            sx={{
              border: '1px solid #dee2e6',
              backgroundColor: 'white',
              borderRadius: '4px',
              width: 40,
              height: 40
            }}
          >
            <FilterIcon sx={{ color: 'white', fontSize: 20 }} />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add></Add>}
            sx={buttonStyle}
          >
            {/* <PlusIcon sx={{ color: 'white', backgroundColor: 'white', mr: 1 }} /> */}
            Add Tenant
          </Button>
        </Box>
      </Box>
      <Box>
        {tenants && (
          <Table
            data={tenants || []}
            columns={tenantTableColumns}
            tablePropsObject={{
              tableHeadProps: { sx: tableHeadProps },
              columnCellProps: { sx: coloumnCellProps },
              tableContainerProps: { sx: { border: '1px solid #DBDBDB' } },
              bodyCellProps: { sx: bodyCellProps },
              bodyRowProps: { sx: bodyRowProps }
            }}
            limit={limit}
            setLimit={setLimit}
            offset={offset}
            setOffset={setOffset}
            count={Number(tenants?.count) || 0}
            manualPagination={true}
            enableSorting={true}
            handleSortColumnChange={setSortBy}
            enablePagination={true}
          />
        )}
      </Box>


    </Box>


  );
};

export default Tenant;
