import { colors } from "Providers/theme/colors"

export const headerBoxStyle = {
    mb: 3,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
}

export const leftHeaderStyle = {
    fontWeight: 700,
    color: colors.dark,
}

export const buttonStyle = {
    backgroundColor: colors.secondary,
    color: 'white',
    '&:hover': { backgroundColor: colors.secondary650 },
    textTransform: 'none',
    fontWeight: 700,
    fontSize: '0.8125rem',
    borderRadius: '0.375rem'
}

export const tableHeadProps = {
    backgroundColor: colors.lightGreyTableHeader,
    borderColor: colors.dark100,
    color: colors.dark900
}

export const coloumnCellProps = {
    fontWeight: 700,
    fontSize: '0.8125rem',
    color: colors.dark900,
}

export const bodyCellProps = {
    color: colors.dark800,
    fontWeight: 500,
    fontSize: '0.8125rem',
}

export const bodyRowProps = {
    padding: '14px',
    borderBottom: '2px solid #F1F1F1'
}

export const actionStyles = {
    color: colors.dark500,
    fontSize: '1.25rem',
    fill: 'white',
    mr: 2
}