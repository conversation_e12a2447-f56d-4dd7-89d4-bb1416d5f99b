import {
  Box,
  FormControl,
  MenuItem,
  Table as MuiTable,
  TableProps as MuiTableProps,
  Pagination,
  Paper,
  Select,
  TableBody,
  TableBodyProps,
  TableCellProps,
  TableContainer,
  TableContainerProps,
  TableFooterProps,
  TableHead,
  TableHeadProps,
  TablePaginationProps,
  TableRow,
  TableRowProps,
  Typography
} from '@mui/material';
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  SortingState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable
} from '@tanstack/react-table';
import { JSX, memo, useMemo, useState } from 'react';
import { filterFns } from './FilterFunctions';
import { DefaultColumn, DefaultRow, GlobalFilter } from './helper';

export interface AnyObject {
  [key: string]: any; // NOSONAR
}

export type MUITablePropsObject = {
  tableContainerProps?: TableContainerProps;
  tableProps?: MuiTableProps;
  tableHeadProps?: TableHeadProps;
  headerRowProps?: TableRowProps;
  tableBodyProps?: TableBodyProps;
  tableFooterProps?: TableFooterProps;
  tablePaginationProps?: TablePaginationProps;
  bodyRowProps?: TableRowProps;
  bodyCellProps?: TableCellProps;
  columnCellProps?: TableCellProps;
};

const columnNameMap: Record<string, string> = {
  tenantName: 'name',
  createdDate: 'createdOn',
  updatedAt: 'modifiedOn',
  leadName: 'firstName',
  phone: 'phoneNumber',
  employer: 'companyName',
  numberOfEmployee: 'employeeCount',
  createdAt: 'createdOn',
  planName: 'name',
  price: 'amount',
};

export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

export interface TableProps<T extends AnyObject> {
  data: T[];
  tableName?: string;
  limit?: number;
  setLimit?: (newLimit: number) => void;
  offset?: number;
  setOffset?: (newOffset: number) => void;
  count?: number;
  manualPagination?: boolean;
  columns: ColumnDef<T>[];
  enableSorting?: boolean;
  enableGlobalFiltering?: boolean;
  globalFilterFn?: FilterFn<T>;

  enableColumnFiltering?: boolean;
  enablePagination?: boolean;
  rowsPerPageOptions?: Array<number | { label: string; value: number }>;
  tablePropsObject?: MUITablePropsObject;
  handleSortColumnChange?: (sortBy: string | null) => void;
}
const DEFAULT_ROWS_PER_PAGE = 5;
const ROWS_PER_PAGE_OPTION_1 = 10;
const ROWS_PER_PAGE_OPTION_2 = 25;
const ARCTable = <T extends AnyObject>({
  data,
  columns,
  enableSorting,
  enableGlobalFiltering,
  globalFilterFn = filterFns.fuzzy,
  enableColumnFiltering,
  enablePagination,
  rowsPerPageOptions = [
    DEFAULT_ROWS_PER_PAGE,
    ROWS_PER_PAGE_OPTION_1,
    ROWS_PER_PAGE_OPTION_2,
    { label: 'All', value: data.length },
  ],
  manualPagination = false,
  handleSortColumnChange,
  tablePropsObject,
}: TableProps<T>) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTenant, setSelectedTenant] = useState<string | null>(null);
  const tableData = useMemo(() => data, [data]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const handleSortingChange = (updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
    const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue;
    if (!handleSortColumnChange) return;
    if (newSorting.length > 0) {
      const { id, desc } = newSorting[0]; // Get the first sorting column and order
      handleSortColumnChange(`${getBackendColumnName(id)} ${desc ? 'desc' : 'asc'}`);
    } else {
      handleSortColumnChange(null);
    }

    setSorting(newSorting);
  };
  const table = useReactTable({
    data: tableData,
    columns,
    state: {
      sorting,
      globalFilter,
      columnFilters,
    },
    globalFilterFn,
    manualPagination,
    manualSorting: true,
    onSortingChange: handleSortingChange,
    onGlobalFilterChange: setGlobalFilter,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const { getHeaderGroups, getRowModel } = table;
  const { pageSize, pageIndex } = table.getState().pagination;

  return (
    <>
      <TableContainer component={Paper} elevation={0}  {...tablePropsObject?.tableContainerProps}>
        {enableGlobalFiltering && <GlobalFilter globalFilter={globalFilter} setGlobalFilter={setGlobalFilter} />}
        <MuiTable {...tablePropsObject?.tableProps}>
          <TableHead {...tablePropsObject?.tableHeadProps}>
            {getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} {...tablePropsObject?.headerRowProps}>
                {headerGroup.headers.map((header, index) => (
                  <DefaultColumn
                    key={header.id}
                    header={header}
                    index={index}
                    enableColumnFiltering={enableColumnFiltering}
                    enableSorting={enableSorting}
                    columnCellProps={tablePropsObject?.columnCellProps}
                  />
                ))}
              </TableRow>
            ))}
          </TableHead>
          <TableBody {...tablePropsObject?.tableBodyProps}>
            {getRowModel().rows.map((row, index) => (
              <DefaultRow
                key={row.id}
                row={row}
                index={index}
                bodyRowProps={tablePropsObject?.bodyRowProps}
                bodyCellProps={tablePropsObject?.bodyCellProps}
              />
            ))}
          </TableBody>
          {/* {enablePagination && (
          <TableFooter {...tablePropsObject?.tableFooterProps}>
            <DefaultTablePagination
              rowsPerPageOptions={rowsPerPageOptions}
              count={table.getFilteredRowModel().rows.length}
              pageSize={pageSize}
              pageIndex={pageIndex}
              onPageChange={(_, page) => {
                table.setPageIndex(page);
              }}
              onRowsPerPageChange={e => {
                const DEFAULT_PAGE_SIZE = 10;
                const size = e.target.value ? Number(e.target.value) : DEFAULT_PAGE_SIZE;
                table.setPageSize(size);
              }}
              tablePaginationProps={tablePropsObject?.tablePaginationProps}
            />
          </TableFooter>
        )} */}
        </MuiTable>

      </TableContainer>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: 2,
        backgroundColor: 'white',
        // borderTop: '1px solid #dee2e6'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography sx={{ fontSize: '14px', color: '#495057' }}>Show</Typography>
          <FormControl size="small">
            <Select
              value={rowsPerPage}
              onChange={(e) => {
                setRowsPerPage(Number(e.target.value));
                setPage(1);
              }}
              sx={{
                fontSize: '14px',
                minWidth: 60,
                '& .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid #dee2e6'
                }
              }}
            >
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={20}>20</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
          </FormControl>
          <Typography sx={{ fontSize: '14px', color: '#495057' }}>entries</Typography>
        </Box>

        <Pagination
          count={5}
          page={page}
          // onChange={handlePageChange}
          shape="rounded"
          siblingCount={2}
          boundaryCount={1}
          sx={{
            '& .MuiPaginationItem-root': {
              fontSize: '14px',
              minWidth: '32px',
              height: '32px',
              margin: '0 2px',
              color: '#495057',
              borderRadius: '4px',
              border: '1px solid #dee2e6',
              backgroundColor: 'white',
              '&:hover': {
                backgroundColor: '#e9ecef',
              },
              '&.Mui-selected': {
                backgroundColor: '#3f51b5',
                color: 'white',
                border: '1px solid #3f51b5',
                '&:hover': {
                  backgroundColor: '#303f9f',
                },
              },
            },
            '& .MuiPaginationItem-ellipsis': {
              border: 'none',
              backgroundColor: 'transparent',
            },
            '& .MuiPaginationItem-previousNext': {
              backgroundColor: 'white',
              border: '1px solid #dee2e6',
              '&:hover': {
                backgroundColor: '#e9ecef',
              },
            },
          }}
        />
      </Box>
    </>
  );
};

// React.memo produced typed error while using generic
// Made a type to fix it
// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/37087 refer this issue
type TableType = <T extends AnyObject>(props: TableProps<T>) => JSX.Element;

export const Table = memo(ARCTable) as TableType;
